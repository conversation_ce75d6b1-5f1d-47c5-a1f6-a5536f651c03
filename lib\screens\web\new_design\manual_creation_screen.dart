import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'widgets/sidebar.dart';
import '../../../providers/manual_creation_provider.dart';
import '../../../models/role_info.dart';
import 'widgets/chat_widgets/build_role_card.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ManualCreationProvider(),
      child: _ManualCreationScreenContent(),
    );
  }
}

class _ManualCreationScreenContent extends StatefulWidget {
  @override
  _ManualCreationScreenContentState createState() => _ManualCreationScreenContentState();
}

class _ManualCreationScreenContentState extends State<_ManualCreationScreenContent> {
  String? _lastShownError;
  String? _lastShownResult;

  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        // Show alert dialog for validation results
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showValidationAlertDialog(context, provider);
        });

        return _buildMainContent(context, provider);
      },
    );
  }

  Widget _buildMainContent(BuildContext context, ManualCreationProvider provider) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Row(
            children: [

              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // SizedBox(height: 20), // Align with text field

                      // Agents icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/agent.svg',
                        tooltipText: 'Agents',
                        onTap: provider.handleAgentsTap,
                      ),

                      SizedBox(height: 10),

                      // DataSets icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/cube-box.svg',
                        tooltipText: 'DataSets',
                        onTap: provider.handleDataSetsTap,
                      ),

                      SizedBox(height: 10),

                      // Workflows icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/square-box-uncheck.svg',
                        tooltipText: 'Workflows',
                        onTap: provider.handleWorkflowsTap,
                      ),
                    ],
                  ),
                ),
              ),

              // Main content area
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: AppSpacing.sm),
                    // Header with back button
                    GestureDetector(
                       onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.home;
            },
                      child: MouseRegion(
                         cursor: SystemMouseCursors.click,
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/arrow-left.svg',
                              height: 12,
                              width: 12,
                              color: Colors.black,
                            ),
                            // IconButton(
                            //   icon: Icon(Icons.arrow_back, color: Colors.black),
                            //   onPressed: () {
                            //     Navigator.of(context).pop();
                            //   },
                            //),
                            SizedBox(width: 8),
                            Text(
                              'Previous page',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xff5D5D5D),
                                fontWeight: FontWeight.w400,
                                fontFamily: 'TiemposText',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 24),

                    // Title
                    Text(
                      provider.showAgentTable ? 'Agents' : 'Create Your Agents',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'TiemposText',
                        color: Colors.black,
                      ),
                    ),

                    SizedBox(height: 2),

                    // Main content area with icons and text field
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          
                          // Left side - Icons column

                          // SizedBox(width: 16),

                          // Right side - Large text field or agent table
                          Expanded(
                            child: provider.showAgentTable
                                ? _buildAgentTable(provider)
                                : Stack(
                                    children: [
                                      // Main text field
                                      Container(
                                        // height: 500,
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: Colors.grey.shade400, width: 1),
                                          borderRadius: BorderRadius.circular(2),
                                        ),
                                        child: Theme(
                                          data: Theme.of(context).copyWith(
                                            splashColor: Colors.transparent,
                                            highlightColor: Colors.transparent,
                                            hoverColor: Colors.transparent,
                                            focusColor: Colors.transparent,
                                          ),
                                          child: TextField(
                                            controller: provider.textController,
                                            maxLines: null,
                                            expands: true,
                                            textAlignVertical: TextAlignVertical.top,
                                            decoration: InputDecoration(
                                              //  hintText: 'Type your message here...',
                                              hintStyle: TextStyle(
                                                color: Colors.grey.shade500,
                                                fontSize: 16,
                                                fontFamily: 'TiemposText',
                                              ),
                                              border: InputBorder.none,
                                              enabledBorder: InputBorder.none,
                                              focusedBorder: InputBorder.none,
                                              contentPadding: EdgeInsets.all(16),
                                            ),
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontFamily: 'TiemposText',
                                              color: Colors.black,
                                            ),
                                          //  cursorColor: Colors.black,
                                          ),
                                        ),
                                      ),

                                      // Tooltip overlay
                                      if (provider.hoveredIcon != null)
                                        Positioned(
                                          top: provider.hoveredIcon == 'Agents'
                                              ? 210
                                              : provider.hoveredIcon == 'DataSets'
                                                  ? 230
                                                  :
                                                  provider.hoveredIcon == 'Workflows'
                                                      ? 255:0,
                                          //210,
                                          left: 2,
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                            decoration: BoxDecoration(
                                              color: Colors.black,
                                            //  borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              provider.hoveredIcon!,
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 12,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'TiemposText',
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 8),

                    // Bottom buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Cancel button
                        TextButton(
                          onPressed: () {
                           // Navigator.of(context).pop();
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 10),
                            side:
                                BorderSide(color: Color(0xffD0D0D0), width: 1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),

                        SizedBox(width: 20),

                        // Validate button
                        ElevatedButton(
                          onPressed: provider.isValidating ? null : provider.handleValidate,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: provider.isValidating
                                ? Colors.grey.shade400
                                : Color(0xff0058FF),
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: provider.isValidating
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Validating...',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14,
                                        fontFamily: 'TiemposText',
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  provider.showAgentTable ? 'Next' : 'Validate',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontFamily: 'TiemposText',
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                        ),

                      ],
                    ),
                   SizedBox(height: 8),
                  ],
                ),
              ),
              Expanded(child: SizedBox()),
            ],
          ),
        );
  }

  // Show validation results in alert dialog
  void _showValidationAlertDialog(BuildContext context, ManualCreationProvider provider) {
    // Only show alert dialog if there's a new validation result or error
    if (provider.validationError != null && _lastShownError != provider.validationError) {
      _lastShownError = provider.validationError;
      _showErrorAlertDialog(context, 'Validation Error', provider.validationError!);
    } else if (provider.validationResult != null &&
               provider.validationResult!.validationErrors != null &&
               provider.validationResult!.validationErrors!.isNotEmpty) {
      // Show validation errors in alert dialog
      final errors = provider.validationResult!.validationErrors!;
      final errorMessages = errors.map((e) => '• ${e.message ?? 'Validation error'}').toList();

      if (_lastShownResult != errorMessages.join('\n')) {
        _lastShownResult = errorMessages.join('\n');
        _showValidationErrorsAlertDialog(context, 'Validation Errors', errorMessages);
      }
    } else if (provider.validationResult != null &&
               provider.showAgentTable &&
               _lastShownResult != 'success') {
      // Show success message when agent table is shown
      _lastShownResult = 'success';
      _showSuccessAlertDialog(context, 'Validation Successful', 'Agent data loaded successfully.');
    }
  }

  // Show error alert dialog
  void _showErrorAlertDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
               maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show validation errors alert dialog
  void _showValidationErrorsAlertDialog(BuildContext context, String title, List<String> messages) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.warning_outlined, color: Colors.orange, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.orange,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Container(
                  width: double.maxFinite,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: messages.map((message) => Padding(
                      padding: EdgeInsets.only(bottom: 8),
                      child: Text(
                        message,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'TiemposText',
                          color: Colors.black87,
                        ),
                      ),
                    )).toList(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show success alert dialog
  void _showSuccessAlertDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.4,
                maxHeight: MediaQuery.of(context).size.height * 0.8,
              ),
              child: AlertDialog(
                title: Row(
                  children: [
                    Icon(Icons.check_circle_outline, color: Colors.green, size: 24),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'TiemposText',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                content: Text(
                  '• $message',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'TiemposText',
                    color: Colors.black87,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'OK',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: 'TiemposText',
                        color: Color(0xff0058FF),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Build agent table similar to web_home_screen_chat.dart
  Widget _buildAgentTable(ManualCreationProvider provider) {
    if (provider.extractedAgentData == null ||
        provider.extractedAgentData!.agents == null ||
        provider.extractedAgentData!.agents!.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400, width: 1),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Center(
          child: Text(
            'No agent data available',
            style: TextStyle(
              fontSize: 16,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400, width: 1),
        borderRadius: BorderRadius.circular(2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'Roles and Use Cases',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${provider.extractedAgentData!.agents!.length} Agent${provider.extractedAgentData!.agents!.length != 1 ? 's' : ''}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'TiemposText',
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Agent rows
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.all(16),
              itemCount: provider.extractedAgentData!.agents!.length,
              itemBuilder: (context, index) {
                final agent = provider.extractedAgentData!.agents![index];

                // Convert AgentInfo to RoleInfo for BuildRoleCard
                final role = RoleInfo(
                  id: agent.id,
                  title: agent.title,
                  description: agent.description,
                  version: agent.version,
                  createdBy: agent.createdBy,
                  createdDate: _formatDate(agent.createdDate ?? DateTime.now()),
                  modifiedBy: agent.modifiedBy,
                  modifiedDate: _formatDate(agent.modifiedDate ?? DateTime.now()),
                  // Extract use cases from agent sections
                  useCases: agent.sections
                      .where((section) => section.title.toLowerCase().contains('responsibility'))
                      .expand((section) => section.items)
                      .toList(),
                  // Extract permissions from agent sections
                  permissions: {
                    'entities': agent.sections
                        .where((section) => section.title.toLowerCase().contains('authority'))
                        .expand((section) => section.items)
                        .toList(),
                    'objectives': agent.sections
                        .where((section) => section.title.toLowerCase().contains('kpi'))
                        .expand((section) => section.items)
                        .toList(),
                  },
                );

                return Padding(
                  padding: EdgeInsets.only(bottom: 8),
                  child: BuildRoleCard(
                    role: role,
                    isSelected: false,
                    onRoleTap: (selectedRole) {
                      // Handle role tap if needed
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        provider.setHoveredIcon(tooltipText);
      },
      onExit: (_) {
        provider.clearHoveredIcon();
      },
      child: GestureDetector(
        onTap: onTap,
        child: SvgPicture.asset(
          iconPath,
          width: 11,
          height: 11,
          color: Color(0xff797676),
        ),
      ),
    );
  }
}
