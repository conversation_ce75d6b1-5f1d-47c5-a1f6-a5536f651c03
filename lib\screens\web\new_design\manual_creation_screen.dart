import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'widgets/sidebar.dart';
import '../../../providers/manual_creation_provider.dart';

class ManualCreationScreen extends StatelessWidget {
  const ManualCreationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ManualCreationProvider(),
      child: _ManualCreationScreenContent(),
    );
  }
}

class _ManualCreationScreenContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ManualCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: Row(
            children: [
             
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 5.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // SizedBox(height: 20), // Align with text field

                      // Agents icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/agent.svg',
                        tooltipText: 'Agents',
                        onTap: provider.handleAgentsTap,
                      ),

                      SizedBox(height: 10),

                      // DataSets icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/cube-box.svg',
                        tooltipText: 'DataSets',
                        onTap: provider.handleDataSetsTap,
                      ),

                      SizedBox(height: 10),

                      // Workflows icon
                      _buildIconWithTooltip(
                        provider: provider,
                        iconPath: 'assets/images/square-box-uncheck.svg',
                        tooltipText: 'Workflows',
                        onTap: provider.handleWorkflowsTap,
                      ),
                    ],
                  ),
                ),
              ),

              // Main content area
              Expanded(
                flex: 7,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: AppSpacing.sm),
                    // Header with back button
                    GestureDetector(
                       onTap: () {
              Provider.of<WebHomeProvider>(context, listen: false)
                  .currentScreenIndex = ScreenConstants.home;
            },
                      child: MouseRegion(
                         cursor: SystemMouseCursors.click,
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              'assets/images/arrow-left.svg',
                              height: 12,
                              width: 12,
                              color: Colors.black,
                            ),
                            // IconButton(
                            //   icon: Icon(Icons.arrow_back, color: Colors.black),
                            //   onPressed: () {
                            //     Navigator.of(context).pop();
                            //   },
                            //),
                            SizedBox(width: 8),
                            Text(
                              'Previous page',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xff5D5D5D),
                                fontWeight: FontWeight.w400,
                                fontFamily: 'TiemposText',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 24),

                    // Title
                    Text(
                      'Create Your Agents',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'TiemposText',
                        color: Colors.black,
                      ),
                    ),

                    SizedBox(height: 2),

                    // Main content area with icons and text field
                    Expanded(
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left side - Icons column

                          // SizedBox(width: 16),

                          // Right side - Large text field
                          Expanded(
                            child: Stack(
                              children: [
                                // Main text field
                                Container(
                                  // height: 500,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.grey.shade400, width: 1),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                  child: Theme(
                                    data: Theme.of(context).copyWith(
                                      splashColor: Colors.transparent,
                                      highlightColor: Colors.transparent,
                                      hoverColor: Colors.transparent,
                                      focusColor: Colors.transparent,
                                    ),
                                    child: TextField(
                                      controller: provider.textController,
                                      maxLines: null,
                                      expands: true,
                                      textAlignVertical: TextAlignVertical.top,
                                      decoration: InputDecoration(
                                        //  hintText: 'Type your message here...',
                                        hintStyle: TextStyle(
                                          color: Colors.grey.shade500,
                                          fontSize: 16,
                                          fontFamily: 'TiemposText',
                                        ),
                                        border: InputBorder.none,
                                        enabledBorder: InputBorder.none,
                                        focusedBorder: InputBorder.none,
                                        contentPadding: EdgeInsets.all(16),
                                      ),
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontFamily: 'TiemposText',
                                        color: Colors.black,
                                      ),
                                    //  cursorColor: Colors.black,
                                    ),
                                  ),
                                ),

                                // Tooltip overlay
                                if (provider.hoveredIcon != null)
                                  Positioned(
                                    top: provider.hoveredIcon == 'Agents'
                                        ? 210
                                        : provider.hoveredIcon == 'DataSets'
                                            ? 230
                                            :
                                            provider.hoveredIcon == 'Workflows'
                                                ? 255:0,
                                    //210,
                                    left: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: Colors.black,
                                      //  borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        provider.hoveredIcon!,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'TiemposText',
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 8),

                    // Bottom buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Cancel button
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 10),
                            side:
                                BorderSide(color: Color(0xffD0D0D0), width: 1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),

                        SizedBox(width: 20),

                        // Validate button
                        ElevatedButton(
                          onPressed: provider.handleValidate,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xff0058FF),
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          child: Text(
                            'Validate',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'TiemposText',
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                    
                      ],
                    ),
                   SizedBox(height: 8),
                  ],
                ),
              ),
              Expanded(child: SizedBox()),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIconWithTooltip({
    required ManualCreationProvider provider,
    required String iconPath,
    required String tooltipText,
    required VoidCallback onTap,
  }) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (_) {
        provider.setHoveredIcon(tooltipText);
      },
      onExit: (_) {
        provider.clearHoveredIcon();
      },
      child: GestureDetector(
        onTap: onTap,
        child: SvgPicture.asset(
          iconPath,
          width: 11,
          height: 11,
          color: Color(0xff797676),
        ),
      ),
    );
  }
}
