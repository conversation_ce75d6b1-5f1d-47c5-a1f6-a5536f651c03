import 'package:flutter/material.dart';

class ManualCreationProvider extends ChangeNotifier {
  late TextEditingController _textController;
  String? _hoveredIcon;

  ManualCreationProvider() {
    _textController = TextEditingController();
  }

  // Getters
  TextEditingController get textController => _textController;
  String? get hoveredIcon => _hoveredIcon;

  // Methods
  void setHoveredIcon(String? iconName) {
    _hoveredIcon = iconName;
    notifyListeners();
  }

  void clearHoveredIcon() {
    _hoveredIcon = null;
    notifyListeners();
  }

  void clearText() {
    _textController.clear();
    notifyListeners();
  }

  void handleAgentsTap() {
    // Handle agents tap logic
  }

  void handleDataSetsTap() {
    // Handle datasets tap logic
  }

  void handleWorkflowsTap() {
    // Handle workflows tap logic
  }

  void handleValidate() {
    // Handle validate action
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      // Process the validation logic here
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
